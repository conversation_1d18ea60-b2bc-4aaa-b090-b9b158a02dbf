<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/Styles/TemplateUSS.uss?fileID=7433441132597879392&amp;guid=eaa1ff4a10c3d497692244c7090d4c77&amp;type=3#TemplateUSS" />
    <SnowEater.Core.UI.TitleScreen class="WindowHolder-Size">
        <ui:VisualElement name="background" style="flex-grow: 1; background-color: rgb(36, 106, 76);">
            <ui:Label text="Template Game" name="title-screen__title-label" style="align-self: center; top: 10%; position: absolute; font-size: 100%; -unity-font-style: bold;" />
            <ui:VisualElement name="VisualElement" style="flex-grow: 1; justify-content: flex-end; align-items: center; padding-bottom: 1%;">
                <ui:Button name="title-screen__start-new-game-btn" text="Start" class="TitleScreen-Btn" />
                <ui:Button text="Continue&#10;" name="title-screen__continue-game-btn" class="TitleScreen-Btn" />
                <ui:Button text="Settings" name="title-screen__settings-btn" class="TitleScreen-Btn" />
                <ui:Button name="title-screen__quit-game-btn" class="TitleScreen-Btn">
                    <SnowEater.Core.UI.LocalizedTextElement key="Common_Yes" />
                </ui:Button>
            </ui:VisualElement>
        </ui:VisualElement>
    </SnowEater.Core.UI.TitleScreen>
</ui:UXML>
