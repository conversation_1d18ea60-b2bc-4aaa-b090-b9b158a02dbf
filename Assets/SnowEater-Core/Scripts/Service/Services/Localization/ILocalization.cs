// Copyright Snow Eater Studios

using SnowEater.Core.Localization;
using SnowEater.Core.Systems;
using UnityEngine.UIElements;

namespace SnowEater.Core.Services
{
    public interface ILocalization : IService
    {
        public LocalizationLanguageEnum CurrentLanguage { get; }
        public void SetLanguage(LocalizationLanguageEnum language);
        public string GetLocalizedText(string key);
        public void BindLocTermLink(TextElement textElement, LocTerm locTerm);
        public void BindLocTermLink(TextElement textElement, LocExpression locExpression);

    }
}