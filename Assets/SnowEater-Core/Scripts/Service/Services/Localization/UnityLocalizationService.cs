// Copyright Isto Inc.

using SnowEater.Core.Localization;
using SnowEater.Core.Systems;
using System;
using UnityEngine;
using UnityEngine.Localization.Settings;
using System.Linq;
using UnityEditor.UIElements;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.UIElements;

namespace SnowEater.Core.Services
{
    [CreateAssetMenu(
        fileName = nameof(UnityLocalizationService),
        menuName = "Snow Eater/Services/" + nameof(UnityLocalizationService))]
    public class UnityLocalizationService : Service, ILocalization
    {
        public override Type GetServiceInterface() => typeof(ILocalization);

        public LocalizationLanguageEnum CurrentLanguage
        {
            get
            {
                var locale = LocalizationSettings.SelectedLocale;
                return LocalizationLanguageEnum.GetByValue(locale.Identifier.Code);
            }
        }

        public async void SetLanguage(LocalizationLanguageEnum language)
        {
            // Ensure the localization system is initialized
            await LocalizationSettings.InitializationOperation.Task;

            // Find the locale matching the enum's locale code
            var locale = LocalizationSettings.AvailableLocales.Locales
                .FirstOrDefault(l => l.Identifier.Code == language.Value);

            if (locale != null)
            {
                LocalizationSettings.SelectedLocale = locale;

            }
            else
            {
                Debug.LogWarning($"Locale '{language.Value}' not found.");
            }
        }

        public string GetLocalizedText(string key)
        {
            // Ensure the localization system is initialized
            if (!LocalizationSettings.InitializationOperation.IsDone)
            {
                LocalizationSettings.InitializationOperation.WaitForCompletion();
            }

            // Search through all tables manually to find the key
            var tables = LocalizationSettings.StringDatabase.GetAllTables();
            tables.WaitForCompletion();

            foreach (var table in tables.Result)
            {
                // Use the table collection name and wait for completion
                var stringOperation =
                    LocalizationSettings.StringDatabase.GetLocalizedStringAsync(table.TableCollectionName, key);
                stringOperation.WaitForCompletion();

                if (stringOperation.Status == AsyncOperationStatus.Succeeded
                    && !string.IsNullOrEmpty(stringOperation.Result))
                {
                    return stringOperation.Result;
                }
            }

            return "LOCALIZATION_NOT_FOUND";
        }

        public void BindLocTermLink(TextElement textElement, LocTerm locTerm)
        {
            // textElement.Bind();


        }

        public void BindLocTermLink(TextElement textElement, LocExpression locExpression)
        {
            // _textElementLocExprDict.TryAdd(textElement, locExpression);
        }
    }
}