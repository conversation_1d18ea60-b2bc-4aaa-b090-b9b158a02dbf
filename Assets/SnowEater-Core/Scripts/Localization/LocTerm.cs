// Copyright Snow Eater Studios

using SnowEater.Core.Services;
using SnowEater.Core.Systems;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.Localization
{
    public abstract class LocTerm
    {
        protected readonly string _key;

        private ILocalization _localizationService;
        private bool _serviceInitialized = false;

        protected LocTerm(string key)
        {
            _key = key;
            // Don't initialize service in constructor to avoid null reference exceptions
            // when Services system isn't available (e.g., in editor, UI Builder)
        }

        public string GetKey()
        {
            return _key;
        }

        public virtual string Localize()
        {
            // Lazy initialization of the localization service
            if (!_serviceInitialized)
            {
                TryInitializeService();
            }

            // If service is available, use it; otherwise return the key as fallback
            if (_localizationService != null)
            {
                return _localizationService.GetLocalizedText(_key);
            }
            else
            {
                // Fallback when service isn't available (editor, early initialization, etc.)
                return _key;
            }
        }

        private void TryInitializeService()
        {
            _serviceInitialized = true;

            // Only try to get the service if we're in play mode and the Services system is available
            if (Application.isPlaying && Systems.Services.Instance != null)
            {
                try
                {
                    if (Systems.Services.Has<ILocalization>())
                    {
                        _localizationService = Systems.Services.Get<ILocalization>();
                    }
                }
                catch (System.Exception ex)
                {
                    // Log the exception but don't crash - we'll use fallback behavior
                    Debug.LogWarning($"Failed to get ILocalization service: {ex.Message}");
                }
            }
        }

        public void LocalizeInto(TextElement textElement)
        {
            if(textElement == null) return;
             textElement.text = Localize();
        }

        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }

            LocTerm other = (LocTerm)otherObject;
            return _key.Equals(other._key);
        }

        public override int GetHashCode()
        {
            return _key != null ? _key.GetHashCode() : 0;
        }
    }
}