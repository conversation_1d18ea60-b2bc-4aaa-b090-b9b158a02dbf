using SnowEater.Core.Events;
using System;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.Localization.Settings;
using SnowEater.Core.Services;      // for Systems.Services.Get<ILocalization>()
using SnowEater.Core.Localization;

namespace SnowEater.Core.UI
{
    /// <summary>
    /// A TextElement that automatically fetches its .text from a LocTerm
    /// and updates whenever the locale changes. Provides an Editor‐preview fallback.
    /// </summary>
    public class LocalizedTextElement : TextElement, IDisposable
    {
        // Needed so UI Builder/UXML can create one without arguments.
        public new class UxmlFactory : UxmlFactory<LocalizedTextElement, UxmlTraits> { }

        private LocTerm _locTerm;
        private ILocalization _localizationService;

        /// <summary>
        /// Construct in C# by passing in a LocTerm directly.
        /// </summary>
        public LocalizedTextElement(LocTerm locTerm, string ussClass = null)
        {
            if (locTerm == null) throw new ArgumentNullException(nameof(locTerm));
            _locTerm = locTerm;

            if (!string.IsNullOrEmpty(ussClass))
                this.AddToClassList(ussClass);

            // Grab the ILocalization service only if in play mode.
            if (Application.isPlaying)
                _localizationService = Systems.Services.Get<ILocalization>();
        }

        /// <summary>
        /// Parameterless ctor (for UXML). Must call SetLocTerm(...) at runtime or it will show no text.
        /// </summary>
        public LocalizedTextElement()
        {
            // No key assigned yet; service only available at runtime.
        }

        /// <summary>
        /// If constructed via the parameterless ctor (e.g. from UXML),
        /// call this to assign the LocTerm so it can work.
        /// </summary>
        public void SetLocTerm(LocTerm locTerm)
        {
            if (locTerm == null) throw new ArgumentNullException(nameof(locTerm));
            _locTerm = locTerm;
            UpdateText();
        }

        public override void OnAttachToPanel(AttachToPanelEvent evt)
        {
            base.OnAttachToPanel(evt);

            // If the service wasn’t set in the constructor, attempt to get it now.
            if (_localizationService == null && Application.isPlaying)
            {
                _localizationService = Systems.Services.Get<ILocalization>();
            }

            // Only attempt to update if we have a key
            if (_locTerm != null)
                UpdateText();

            // Subscribe to your LanguageChangedEvent so UpdateText() fires at runtime
            EventManager.Subscribe<LanguageChangedEvent>(OnLanguageChanged);
        }

        public override void OnDetachFromPanel(DetachFromPanelEvent evt)
        {
            base.OnDetachFromPanel(evt);

            // Unsubscribe to avoid hanging onto events
            EventManager.Unsubscribe<LanguageChangedEvent>(OnLanguageChanged);
        }

        /// <summary>
        /// Called whenever your custom LanguageChangedEvent is fired.
        /// </summary>
        private void OnLanguageChanged(LanguageChangedEvent evt)
        {
            UpdateText();
        }

        private void UpdateText()
        {
            if (_locTerm == null)
            {
                // No key set, so nothing to display
                base.text = string.Empty;
                return;
            }

            if (_localizationService != null)
            {
                // Runtime: fetch the actual localized string
                string localized = _locTerm.Localize();
                base.text = localized;
            }
            else
            {
                // Editor or not yet playing: show the raw key so you can preview
                base.text = _locTerm.GetKey();
            }
        }

        public void Dispose()
        {
            EventManager.Unsubscribe<LanguageChangedEvent>(OnLanguageChanged);
        }

        #region UXML Traits (if you want to use this in UXML directly)

        public new class UxmlTraits : VisualElement.UxmlTraits
        {
            private readonly UxmlStringAttributeDescription _keyAttr = new UxmlStringAttributeDescription
            {
                name = "key",
                defaultValue = ""
            };

            private readonly UxmlStringAttributeDescription _ussClassAttr = new UxmlStringAttributeDescription
            {
                name = "class",
                defaultValue = ""
            };

            public override void Init(VisualElement ve, IUxmlAttributes bag, CreationContext cc)
            {
                base.Init(ve, bag, cc);

                var element = (LocalizedTextElement)ve;
                string keyString = _keyAttr.GetValueFromBag(bag, cc);
                string ussClass = _ussClassAttr.GetValueFromBag(bag, cc);

                if (!string.IsNullOrEmpty(ussClass))
                    element.AddToClassList(ussClass);

                if (!string.IsNullOrEmpty(keyString))
                    element.SetLocTerm(new GenericLocTerm(keyString));
            }
        }

        #endregion
    }
}