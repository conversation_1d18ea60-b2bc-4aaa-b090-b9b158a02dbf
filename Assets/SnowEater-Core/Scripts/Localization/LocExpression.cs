// Copyright Isto Inc.

using SnowEater.Core.Services;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine.UIElements;

namespace SnowEater.Core.Localization
{
    public abstract class LocExpression
    {
        protected List<LocTerm> _locTerms;
        private ILocalization _localizationService;

        protected LocExpression(List<LocTerm> locTerms)
        {
            _locTerms = locTerms;
            _localizationService = Systems.Services.Get<ILocalization>();
        }

        public string Localize()
        {
            StringBuilder stringBuilder = new StringBuilder();
            foreach (var locTerm in _locTerms)
            {
                stringBuilder.Append(locTerm.Localize());
            }
            return stringBuilder.ToString();
        }

        public void LocalizeInto(TextElement textElement)
        {
            if(textElement == null) return;
            textElement.text = Localize();
            _localizationService.BindLocTermLink(textElement, this);

        }

        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }
            LocExpression other = (LocExpression)otherObject;

            List<LocTerm> thisLocTerms = _locTerms ?? new List<LocTerm>();
            List<LocTerm> otherLocTerms = other._locTerms ?? new List<LocTerm>();

            return thisLocTerms.SequenceEqual(otherLocTerms);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                if (_locTerms == null)
                    return 0;

                int hash = 17;
                foreach (var locTerm in _locTerms)
                {
                    hash *= 31 + (locTerm?.GetHashCode() ?? 0);
                }

                return hash;
            }
        }
    }
}