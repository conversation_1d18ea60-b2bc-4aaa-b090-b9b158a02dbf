// Copyright Snow Eater Studios

using SnowEater.Core.Services;
using SnowEater.Core.Systems;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.Localization
{
    public abstract class LocExpression
    {
        protected List<LocTerm> _locTerms;
        private ILocalization _localizationService;
        private bool _serviceInitialized = false;

        protected LocExpression(List<LocTerm> locTerms)
        {
            _locTerms = locTerms;
            // Don't initialize service in constructor to avoid null reference exceptions
            // when Services system isn't available (e.g., in editor, UI Builder)
        }

        public string Localize()
        {
            StringBuilder stringBuilder = new StringBuilder();
            foreach (var locTerm in _locTerms)
            {
                stringBuilder.Append(locTerm.Localize());
            }
            return stringBuilder.ToString();
        }

        public void LocalizeInto(TextElement textElement)
        {
            if(textElement == null) return;
            textElement.text = Localize();
        }

        public override bool Equals(object otherObject)
        {
            if (otherObject == null || GetType() != otherObject.GetType())
            {
                return false;
            }
            LocExpression other = (LocExpression)otherObject;

            List<LocTerm> thisLocTerms = _locTerms ?? new List<LocTerm>();
            List<LocTerm> otherLocTerms = other._locTerms ?? new List<LocTerm>();

            return thisLocTerms.SequenceEqual(otherLocTerms);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                if (_locTerms == null)
                    return 0;

                int hash = 17;
                foreach (var locTerm in _locTerms)
                {
                    hash *= 31 + (locTerm?.GetHashCode() ?? 0);
                }

                return hash;
            }
        }
    }
}