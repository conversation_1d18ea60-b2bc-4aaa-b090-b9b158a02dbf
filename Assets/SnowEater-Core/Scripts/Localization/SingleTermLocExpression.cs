// Copyright Snow Eater Studios

using System.Collections.Generic;
using System.Linq;

namespace SnowEater.Core.Localization
{
    public class SingleTermLocExpression : LocExpression
    {
        public LocTerm GetTerm()
        {
            return _locTerms.FirstOrDefault();
        }

        public SingleTermLocExpression(LocTerm locTerm) : base(new List<LocTerm> { locTerm })
        {
        }

    }
}