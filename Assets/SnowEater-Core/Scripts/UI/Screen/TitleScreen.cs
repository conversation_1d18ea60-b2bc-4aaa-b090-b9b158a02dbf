// Copyright Snow Eater Studios

using SnowEater.Core.Managers.WindowManager;
using SnowEater.Core.Localization;
using SnowEater.Core.Services;
using SnowEater.Core.UI.Modals;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.UI
{
    [UxmlElement]
    public partial class TitleScreen : Window
    {
        private static readonly string START_NEW_GAME_BUTTON = "title-screen__start-new-game-btn";
        private static readonly string CONTINUE_GAME_BUTTON = "title-screen__continue-game-btn";
        private static readonly string GAME_SETTINGS_BUTTON = "title-screen__settings-btn";
        private static readonly string QUIT_GAME_BUTTON = "title-screen__quit-game-btn";

        Button _startNewGameButton;
        Button _continueGameButton;
        Button _gameSettingsButton;
        Button _quitGameButton;

        private IWindowManagerService _windowManagerService;
        private ILocalization _localizationService;


        public TitleScreen()
        {
            // HomeScreenController.ShowLevelInfo += OnShowLevelInfo;
        }

        ~TitleScreen()
        {
            // HomeScreenController.ShowLevelInfo -= OnShowLevelInfo;
        }

        public override void ShowWindow()
        {
            base.ShowWindow();
            Initialize();
            _windowManagerService = Systems.Services.Get<IWindowManagerService>();
            _localizationService = Systems.Services.Get<ILocalization>();
        }

        void Initialize()
        {
            SetupNewGameButton();
            SetupContinueGameButton();
            SetupGameSettingsButton();
            SetupQuitGameButton();
        }

        private void SetupNewGameButton()
        {
            _startNewGameButton = contentContainer.Q(START_NEW_GAME_BUTTON) as Button;
            if (_startNewGameButton != null)
            {
                // Ensure the element can be focused by navigation.
                _startNewGameButton.focusable = true;
                Focus(); // Focus to this window panel first. //TODO: Perhaps this needs to get integrated into base.ShowWindow()? Might cause issue's if there are multiple windows though.
                _startNewGameButton.Focus();
                // Register callbacks for both mouse and controller/keyboard input.
                _startNewGameButton.RegisterCallback<ClickEvent>(OnSubmit_ReturnToGame);
                _startNewGameButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_ReturnToGame);

            }
        }
        private void SetupContinueGameButton()
        {
            _continueGameButton = contentContainer.Q(CONTINUE_GAME_BUTTON) as Button;
            if (_continueGameButton != null)
            {
                // Ensure the element can be focused by navigation.
                _continueGameButton.focusable = true;
                // Register callbacks for both mouse and controller/keyboard input.
                _continueGameButton.RegisterCallback<ClickEvent>(OnSubmit_ContinueGame);
                _continueGameButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_ContinueGame);
            }
        }

        private void SetupGameSettingsButton()
        {
            _gameSettingsButton = contentContainer.Q(GAME_SETTINGS_BUTTON) as Button;
            if (_gameSettingsButton != null)
            {
                // Ensure the element can be focused by navigation.
                _gameSettingsButton.focusable = true;
                // Register callbacks for both mouse and controller/keyboard input.
                _gameSettingsButton.RegisterCallback<ClickEvent>(OnSubmit_GameSettings);
                _gameSettingsButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_GameSettings);
            }
        }
        private void SetupQuitGameButton()
        {
            _quitGameButton = contentContainer.Q(QUIT_GAME_BUTTON) as Button;
            if (_quitGameButton != null)
            {
                // Ensure the element can be focused by navigation.
                _quitGameButton.focusable = true;
                // Register callbacks for both mouse and controller/keyboard input.
                _quitGameButton.RegisterCallback<ClickEvent>(OnSubmit_QuitGame);
                _quitGameButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_QuitGame);
                // var term = LocTermFactory.Create(LocalizationType.Localized, "Common_Quit");
                // _quitGameButton.Q<LocalizedTextElement>().SetLocTerm(term);
            }
        }

        private void OnSubmit_ReturnToGame(EventBase evt)
        {
            var data = new YesNoModalData
            {
                Title             = "Are you sure?",
                Content           = "Do you want to proceed with this action?",
                ShowConfirmButton = true,
                ConfirmButtonText = "Yes",
                ShowCancelButton  = true,
                CancelButtonText  = "No",
                OnCancel = () =>
                {
                    Debug.Log("Cancelled");
                },
                OnConfirm = () =>
                {
                    Debug.Log("Confirmed");
                }
                // you can also set data.overrideLayer = "ModalLayer";
            };

            _windowManagerService.PushWindow<YesNoModal, YesNoModalData>(data);
        }

        private void OnSubmit_ContinueGame(EventBase evt)
        {
            _localizationService.SetLanguage(LocalizationLanguageEnum.FRENCH);
        }

        private void OnSubmit_GameSettings(EventBase evt)
        {
            var term = LocTermFactory.Create(LocalizationType.Localized, "Common_Yes");
            Debug.Log(term.Localize());
        }

        private void OnSubmit_QuitGame(EventBase evt)
        {

        }

    }
}